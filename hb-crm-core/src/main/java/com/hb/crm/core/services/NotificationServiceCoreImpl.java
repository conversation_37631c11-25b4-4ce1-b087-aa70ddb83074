package com.hb.crm.core.services;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import com.hb.crm.core.CashService.CashService;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.MediaWrapper;
import com.hb.crm.core.beans.Notfication;
import com.hb.crm.core.beans.Notification.*;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.clientNotification.GroupedNotificationsDto;
import com.hb.crm.core.dtos.clientNotification.NotificationDto;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.services.interfaces.NotificationService;
import com.hb.crm.core.services.interfaces.TwilioService;
import com.hb.crm.core.util.ApplicationUtil;
import com.hb.crm.core.util.NotificationValidationUtil;
import com.resend.Resend;
import com.resend.core.exception.ResendException;
import com.resend.services.emails.model.CreateEmailOptions;
import com.resend.services.emails.model.CreateEmailResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NotificationServiceCoreImpl implements NotificationService {

    private static final int ALL_LIMIT = 99999;
    private final NotificationSettingRepository notificationSettingRepository;
    private final NotificationRepository notificationRepository;
    private final ModelMapper modelMapper;
    private final MongoTemplate mongoTemplate;
    private final UserRepository userRepository;
    private final CashService cashService;
    private final TwilioService twilioService;
    private final SubPackageRepository subPackageRepository;
    private final NotificationTemplateRepository notificationTemplateRepository;
    private final NotificationMuteRepository notificationMuteRepository;
    private final NotificationMuteUserRepository notificationMuteUserRpository;
    private final Resend resend = new Resend("re_EeVdngv7_7T78E1BFyxZ5yTw3PpSmbCDX");

    public NotificationServiceCoreImpl(NotificationSettingRepository notificationSettingRepository,
                                       NotificationRepository notificationRepository, ModelMapper modelMapper, MongoTemplate mongoTemplate, UserRepository userRepository, CashService cashService, TwilioService twilioService, SubPackageRepository subPackageRepository, NotificationTemplateRepository notificationTemplateRepository, NotificationMuteRepository notificationMuteRepository, NotificationMuteUserRepository notificationMuteUserRpository) {
        this.notificationSettingRepository = notificationSettingRepository;
        this.notificationRepository = notificationRepository;
        this.modelMapper = modelMapper;
        this.mongoTemplate = mongoTemplate;
        this.userRepository = userRepository;
        this.cashService = cashService;
        this.twilioService = twilioService;
        this.subPackageRepository = subPackageRepository;
        this.notificationTemplateRepository = notificationTemplateRepository;
        this.notificationMuteRepository = notificationMuteRepository;
        this.notificationMuteUserRpository = notificationMuteUserRpository;
    }

    @Override
    public boolean sendWhatsAppMessage(String to, String message) {
        try {
            com.twilio.rest.api.v2010.account.Message
                    .creator(
                            new com.twilio.type.PhoneNumber("whatsapp:" + to),
                            new com.twilio.type.PhoneNumber("whatsapp:+***********"),
                            message
                    )
                    .create();

            return true;
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return false;
        }
    }

    @Override
    public boolean sendSmsNotification(String phoneNumber, String message) {
        try {
            twilioService.sendMessage(phoneNumber, message);
            return true;
        } catch (Exception e) {
            log.error("Error while sending SMS notification to {}", phoneNumber, e);
            return false;
        }
    }

    @Override
    public void sendEmail(String email, String subject, String body) {
        // Build the email options
        CreateEmailOptions emailOptions = CreateEmailOptions.builder()
                .from("<EMAIL>")
                .to(email)
                .subject(subject)
                .html(body)
                .build();

        try {
            // Send the email
            CreateEmailResponse response = resend.emails().send(emailOptions);
            System.out.println("Email sent! ID: " + response.getId());
        } catch (ResendException e) {
            System.out.println(e.getMessage());
        }
    }

    private boolean sendEmailNotification(String email, String subject, String body) {
        try {
            CreateEmailOptions emailOptions = CreateEmailOptions.builder()
                    .from("<EMAIL>")
                    .to(email)
                    .subject(subject)
                    .html(body)
                    .build();

            CreateEmailResponse response = resend.emails().send(emailOptions);
            log.info("Email sent successfully! ID: {}", response.getId());
            return true;
        } catch (ResendException e) {
            log.error("Failed to send email to {} : {}", email, e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional
    public void sendAndStoreNotification(String entityId, NotificationType type,
                                         User user, List<Object> entities,
                                         String image, String icon,
                                         NotificationEntityType entityType,
                                         User navigatedUser,
                                         String slug,
                                         String username,
                                         UserType navigatedUserType) {
        try {
            List<NotificationSetting> notificationSettingList = notificationSettingRepository.findByUser(user);
            NotificationSetting notificationSetting;
            if (notificationSettingList == null || notificationSettingList.isEmpty()) {
                var setting = new NotificationSetting();
                setting.setUser(user);
                notificationSetting = notificationSettingRepository.save(setting);
            } else {
                notificationSetting = notificationSettingList.getFirst();
            }


            String token = getUserFCMToken(user);

            processAndSendNotification(type, token, entities,
                    isUrgentNotification(type), notificationSetting, user,
                    entityId, image,
                    icon,
                    entityType,
                    navigatedUser,
                    slug,
                    username,
                    navigatedUserType);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Transactional
    public void processAndSendNotification(NotificationType type, String token, List<Object> entities,
                                           boolean urgent, NotificationSetting setting, User user, String entityId, String image,
                                           String icon,
                                           NotificationEntityType entityType,
                                           User navigatedUser,
                                           String slug,
                                           String username,
                                           UserType navigatedUserType) {
        try {
            Optional<NotificationTemplate> templateOpt = notificationTemplateRepository.findByNotificationTypeAndStatusTrue(type);
            if (templateOpt.isEmpty())
                throw new CustomException(400, "Notification template not found for type: " + type);

            var template = templateOpt.get();

            // Create a map to store variable name -> value mappings
            Map<String, String> templateVariableValues = new HashMap<>();

            List<NotificationTemplateVariable> variables = new ArrayList<>();
            variables.addAll(template.getPush() != null ? template.getPush().getTemplateVariables() : new ArrayList<>());
            variables.addAll(template.getSms() != null ? template.getSms().getTemplateVariables() : new ArrayList<>());
            variables.addAll(template.getEmail() != null ? template.getEmail().getTemplateVariables() : new ArrayList<>());
            variables.addAll(template.getInApp() != null ? template.getInApp().getTemplateVariables() : new ArrayList<>());
            variables.addAll(template.getWhatsApp() != null ? template.getWhatsApp().getTemplateVariables() : new ArrayList<>());

            for (NotificationTemplateVariable templateVariable : variables) {
                String variableName = templateVariable.getName();
                String variableValue = extractValueFromEntities(templateVariable, entities);

                if (variableValue != null) {
                    templateVariableValues.put(variableName, variableValue);
                }
            }

            List<NotificationChannelType> channelsToUse;

            // If the user has pause all notifications generally and the notification is not urgent, don't send it
            if (!setting.isEnableAll() && !urgent)
                return;

            if (urgent) {
                // Send using all template channels
                channelsToUse = template.getChannelTypes();
            } else {
                // check if the user has muted the entity
                NotificationMute mutedEntity = notificationMuteRepository
                        .findByUserIdAndEntityIdAndEntityType(user.getId(), entityId, entityType).orElse(null);

                // check if the user has mute the user for the channels for the type
                NotificationMuteUser notificationMuteUser = notificationMuteUserRpository
                            .findByUserIdAndMutedUser_Id(user.getId(), navigatedUser != null ? navigatedUser.getId(): "-1")
                            .orElse(null);

                // Send using preferred channels (intersection of template channels and user preferences)
                channelsToUse = template.getChannelTypes().stream()
                        .filter(channelType ->
                                !NotificationValidationUtil.isNotificationBlockedForChannel(type, channelType,
                                        setting, mutedEntity, notificationMuteUser))

                        .collect(Collectors.toList());
            }

            // Process each channel
            for (NotificationChannelType channelType : channelsToUse) {
                switch (channelType) {
                    case Push:
                        if (template.getPush() != null) {

                            String processedTitle = replaceTemplateVariables(template.getPush().getSubject(),
                                    templateVariableValues);
                            String processedBody = replaceTemplateVariables(template.getPush().getBody(),
                                    templateVariableValues);

                            Map<String, String> payload = new HashMap<>() {{
                                put("icon", icon != null ? icon : template.getPush().getIcon() == null ? "" : template.getPush().getIcon());
                                put("entityId", entityId == null ? "" : entityId);
                                put("image", image == null ? "" : image);
                                put("slug", slug == null ? "" : slug);
                                put("entityType", entityType == null ? "" : entityType.toString());
                                put("username", username == null ? "" : username);
                                put("navigatedUserImage", navigatedUser == null ? "" : navigatedUser.getProfileImage() == null ? "" : navigatedUser.getProfileImage());
                                put("navigatedUserId", navigatedUser == null ? "" : navigatedUser.getId() == null ? "" : navigatedUser.getId());
                                put("iconType", icon != null ? IconType.IMAGE.toString() : IconType.ICON.toString());
                                put("navigatedUserType", navigatedUserType == null ? "" : navigatedUserType.toString());
                                put("notificationType", type == null ? "" : type.toString());
                            }};

                            var notification = storeNotification(processedTitle, processedBody, type,
                                    NotificationChannelType.Push,
                                    user, false, entityId, image, payload,
                                    icon != null ? icon : template.getPush().getIcon(),
                                    entityType,
                                    navigatedUser,
                                    slug,
                                    username,
                                    icon != null ? IconType.IMAGE : IconType.ICON,
                                    navigatedUserType);

                            payload.put("id", notification.getId());

                            if (token == null) {
                                break;
                            }

                            boolean sent = sendNotification(token, processedTitle,
                                    processedBody, payload);

                            notification.setSent(sent);
                            notificationRepository.save(notification);
                        }
                        break;

                    case Email:
                        if (template.getEmail() != null && setting.isEnableEmailNotification()) {
                            String processedSubject = replaceTemplateVariables(template.getEmail().getSubject(),
                                    templateVariableValues);
                            String processedBody = replaceTemplateVariables(template.getEmail().getBody(),
                                    templateVariableValues);

                            if (user.getUserInfo() == null)
                                break;


                            boolean sent = sendEmailNotification(
                                    user.getUserInfo().getEmail(),
                                    processedSubject, processedBody);

                            storeNotification(processedSubject, processedBody, type, NotificationChannelType.Email,
                                    user, sent, entityId, null, null, null,
                                    null,
                                    navigatedUser,
                                    null,
                                    null,
                                    icon != null ? IconType.IMAGE : IconType.ICON,
                                    navigatedUserType);
                        }
                        break;

                    case Sms:
                        if (template.getSms() != null && setting.isEnableSmsAppNotification()) {
                            String processedMessage = replaceTemplateVariables(template.getSms().getBody(),
                                    templateVariableValues);

                            boolean sent = sendSmsNotification(user.getUserInfo().getMobile(), processedMessage);
                            storeNotification(null, processedMessage, type, NotificationChannelType.Sms, user,
                                    sent, entityId, image, null, null,
                                    null,
                                    navigatedUser,
                                    null,
                                    null,
                                    icon != null ? IconType.IMAGE : IconType.ICON,
                                    navigatedUserType);
                        }
                        break;

                    case InApp:
                        if (template.getInApp() != null) {
                            String processedTitle = replaceTemplateVariables(template.getInApp().getSubject(),
                                    templateVariableValues);
                            String processedBody = replaceTemplateVariables(template.getInApp().getBody(),
                                    templateVariableValues);

                            Map<String, String> payload = new HashMap<>() {{
                                put("icon", icon != null ? icon : template.getPush().getIcon() == null ? "" : template.getPush().getIcon());
                                put("entityId", entityId == null ? "" : entityId);
                                put("image", image == null ? "" : image);
                                put("slug", slug == null ? "" : slug);
                                put("entityType", entityType == null ? "" : entityType.toString());
                                put("username", username == null ? "" : username);
                                put("navigatedUserImage", navigatedUser == null ? "" : navigatedUser.getProfileImage() == null ? "" : navigatedUser.getProfileImage());
                                put("navigatedUserId", navigatedUser == null ? "" : navigatedUser.getId() == null ? "" : navigatedUser.getId());
                                put("iconType", icon != null ? IconType.IMAGE.toString() : IconType.ICON.toString());
                                put("navigatedUserType", navigatedUserType == null ? "" : navigatedUserType.toString());
                                put("notificationType", type == null ? "" : type.toString());
                            }};


                            var notification = storeNotification(processedTitle, processedBody, type,
                                    NotificationChannelType.InApp,
                                    user, false, entityId, image, payload,
                                    icon != null ? icon : template.getPush().getIcon(),
                                    entityType,
                                    navigatedUser,
                                    slug,
                                    username,
                                    icon != null ? IconType.IMAGE : IconType.ICON,
                                    navigatedUserType);

                            payload.put("id", notification.getId());

                            if (token == null)
                                break;

                            boolean sent = sendNotification(token, processedTitle,
                                    processedBody, payload);

                            notification.setSent(sent);
                            notificationRepository.save(notification);
                        }
                        break;

                    default:
                        log.warn("Unsupported notification channel type: {}", channelType);
                        break;
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            log.error(e.getMessage());
        }

    }

    @Override
    @Transactional
    public boolean sendNotification(String token, String title, String body, Map<String, String> payload) {
        try {
            // Make sure to include title/body in the data if needed
            if (payload == null) payload = new HashMap<>();
            payload.put("title", title);
            payload.put("body", body);

            var messageBuilder = Message.builder()
                    .setToken(token)
                    .putAllData(payload); // ONLY data, no .setNotification() and don't use it because its ruin the frontend

            FirebaseMessaging.getInstance().send(messageBuilder.build());
            return true;
        } catch (Exception e) {
            log.error("Error while sending notification", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean sendNotification(String token, NotificationType type, String userId) {
        var user = userRepository.findById(userId).orElseThrow(() -> new CustomException(404, "user not found!"));
        List<NotificationSetting> notificationSettingList = notificationSettingRepository.findByUser(user);
        NotificationSetting notificationSetting;

        if (notificationSettingList == null || notificationSettingList.isEmpty()) {
            var setting = new NotificationSetting();
            setting.setUser(user);
            notificationSetting = notificationSettingRepository.save(setting);
        } else {
            notificationSetting = notificationSettingList.getFirst();
        }

        SubPackage subPackage = subPackageRepository.findById("67f50d3b92d8152941ad90a5")
                .orElseThrow(() -> new CustomException(400, "package not foud!"));
        processAndSendNotification(type, token, List.of(subPackage, user), false, notificationSetting,
                user, subPackage.getId(),
                subPackage.get_package()
                        .getMedias()
                        .stream()
                        .filter(MediaWrapper::isMainImage)
                        .findFirst()
                        .orElse(new MediaWrapper())
                        .getUrl(),
                null,
                NotificationEntityType.PACKAGE,
                subPackage.get_package().getInfulancer(),
                subPackage.getSlug(),
                subPackage.get_package().getInfulancer().getUsername(),
                UserType.Influencer);
        return true;
    }

    @Override
    public void sendBroadcastNotification(String topic, String title, String body) {
        sendBroadcastNotification(topic, title, body, null);
    }

    @Override
    public void sendBroadcastNotification(String topic, String title, String body, Map<String, String> payload) {
        try {
            var messageBuilder = Message.builder()
                    .setTopic(topic)
                    .setNotification(buildNotification(title, body));

            if (payload != null) {
                messageBuilder.putAllData(payload);
            }
            FirebaseMessaging.getInstance().send(messageBuilder.build());
        } catch (Exception e) {
            log.error("Error while sending notification", e);
        }
    }

    private Notification buildNotification(String title, String body) {
        Notification.Builder builder = Notification.builder();
        builder.setTitle(title);
        builder.setBody(body);
        return builder.build();
    }

    @Override
    @Transactional
    public com.hb.crm.core.beans.Notification.Notification storeNotification(String subject, String body, NotificationType type, NotificationChannelType channelType,
                                                                             User user, boolean sent, String entityId, String image, Map<String, String> payload,
                                                                             String icon,
                                                                             NotificationEntityType entityType,
                                                                             User navigatedUser,
                                                                             String slug,
                                                                             String username,
                                                                             IconType iconType,
                                                                             UserType navigatedUserType) {

        com.hb.crm.core.beans.Notification.Notification notification = new com.hb.crm.core.beans.Notification.Notification();
        notification.setSubject(subject);
        notification.setBody(body);
        notification.setSent(sent);
        notification.setUser(user);
        notification.setType(type);
        notification.setChannelType(channelType);
        notification.setLastTriedAt(LocalDateTime.now());
        notification.setEntityId(entityId);
        notification.setImage(image);
        notification.setPayload(payload);
        notification.setIcon(icon);
        notification.setEntityType(entityType);
        notification.setEntitySlug(slug);
        notification.setUsername(username);
        notification.setIconType(iconType);

        if (navigatedUserType != null)
            notification.setNavigatedUsertype(navigatedUserType);

        var savedNotification = notificationRepository.save(notification);

        if (payload != null)
            savedNotification.getPayload().put("id", savedNotification.getId());

        return notificationRepository.save(savedNotification);
    }

    @Override
    public PageDto<NotificationDto> getNotifications(String userId, int page, int size) {
        LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1);
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "lastTriedAt"));

        Page<com.hb.crm.core.beans.Notification.Notification> notificationsPage = notificationRepository.findByUserIdAndLastTriedAtAfterAndHiddenFalseOrderBySentAtDesc(
                userId,
                oneMonthAgo,
                pageable);

        var result = notificationsPage
                .stream()
                .map(item -> modelMapper.map(item, NotificationDto.class))
                .toList();

        for (NotificationDto notificationDto : result) {
            var notificationMute = notificationMuteRepository.findByUserIdAndEntityIdAndEntityType(
                    userId,
                    notificationDto.getEntityId(),
                    notificationDto.getEntityType()
            );

            notificationDto.setMuted(notificationMute.isPresent());
        }

        PageDto<NotificationDto> pageDto = new PageDto<>();
        pageDto.setItems(result);
        pageDto.setTotalNoOfItems(notificationsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);

        return pageDto;
    }

    @Override
    public void updateNotificationReadStatus(String id, boolean read) {
        com.hb.crm.core.beans.Notification.Notification notification = notificationRepository.findById(id)
                .orElseThrow(() -> new CustomException(404, "Notification not found"));
        notification.setRead(read);

        if (read) {
            notification.setReadAt(LocalDateTime.now());
        } else {
            notification.setReadAt(null);
        }

        notificationRepository.save(notification);
    }

    @Override
    public PageDto<NotificationDto> getUnreadNotifications(String userId, int page, int size) {
        LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1);
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "lastTriedAt"));

        Page<com.hb.crm.core.beans.Notification.Notification> notificationsPage = notificationRepository.findUnreadByUserIdAndLastTriedAtAfterAndHiddenFalseOrderBySentAtDesc(
                userId,
                oneMonthAgo,
                pageable);

        var result = notificationsPage
                .stream()
                .map(item -> modelMapper.map(item, NotificationDto.class))
                .toList();

        PageDto<NotificationDto> pageDto = new PageDto<>();
        pageDto.setItems(result);
        pageDto.setTotalNoOfItems(notificationsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);

        return pageDto;
    }

    private boolean isUrgentNotification(NotificationType type) {
        return type == NotificationType.PaymentSucceeded || type == NotificationType.SubmittedSubscribe
                || type == NotificationType.PackageCapacitySubscribedUsers
                || type == NotificationType.PackageCapacityFavouriteUsers;
    }

    @Override
    public Page<com.hb.crm.core.beans.Notification.Notification> search() {
        final Pageable pageable = ApplicationUtil.createPageRequest(0, ALL_LIMIT, "creationDate", "DESC");
        return notificationRepository.findAll(pageable);
    }

    @Override
    public PageDto<com.hb.crm.core.beans.Notification.Notification> search(Map<String, Object> obj, int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }


        PageDto<com.hb.crm.core.beans.Notification.Notification> pageDto = new PageDto<>();
        final Pageable pageable = PageRequest.of(page, limit);
        final Query searchQuery = createSearchSpecification(obj);
        searchQuery.with(Sort.by(Sort.Direction.DESC, "lastTriedAt"));
        searchQuery.with(pageable);
        List<com.hb.crm.core.beans.Notification.Notification> users = mongoTemplate.find(searchQuery, com.hb.crm.core.beans.Notification.Notification.class);
        long count = mongoTemplate.count(searchQuery, Notfication.class);
        pageDto.setTotalNoOfItems(count);
        pageDto.setItems(users);
        return pageDto;

    }

    @Override
    public com.hb.crm.core.beans.Notification.Notification getNotificationById(String id) {
        if (id == null)
            return null;

        final Optional<com.hb.crm.core.beans.Notification.Notification> byId = notificationRepository.findById(id);
        return byId.orElse(null);
    }

    @Override
    public void update(com.hb.crm.core.beans.Notification.Notification obj) {
        notificationRepository.save(obj);
    }

    @Override
    public void delete(String id) {
        notificationRepository.deleteById(id);
    }

    private Query createSearchSpecification(Map<String, Object> obj) {

        Query query = new Query();

        for (Map.Entry<String, Object> entry : obj.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null && !value.toString().isEmpty()) {
                query.addCriteria(Criteria.where(key).is(value.toString()));
            }
        }

        return query;
    }

    @Transactional
//    @Scheduled(fixedDelay = 60000) // every 60 seconds
    public void retryFailedNotifications() {
        List<com.hb.crm.core.beans.Notification.Notification> toRetry = notificationRepository
                .findTop50BySentFalseOrderByCreatedAtAsc();

        for (com.hb.crm.core.beans.Notification.Notification notification : toRetry) {
            try {
                if (notification.getChannelType() != NotificationChannelType.Push &&
                        notification.getChannelType() != NotificationChannelType.InApp) {
                    continue;
                }
                String token = getUserFCMToken(notification.getUser());
                var sent = sendNotification(token, notification.getSubject(), notification.getBody(), notification.getPayload());
                notification.setSent(sent);

            } catch (Exception e) {
                notification.setRetryCount(notification.getRetryCount() + 1);
                log.error("Retry failed for notification ID: {}", notification.getId(), e);
            }

            notification.setLastTriedAt(LocalDateTime.now());
            notificationRepository.save(notification);
        }
    }

    private String extractValueFromEntities(NotificationTemplateVariable variable, List<Object> entities) {
        String propertyName = variable.getName();
        for (Object entity : entities) {
            if (entity == null)
                continue;


            if (!Objects.equals(variable.getEntity(), entity.getClass().getSimpleName())) {
                continue;
            }

            try {
                // Use reflection to get the field value
                Field field = findFieldInClassHierarchy(entity.getClass(), propertyName);
                if (field != null) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    return value != null ? value.toString() : null;
                }

                // Alternative: Try getter method
                String getterName = "get" + capitalize(propertyName);
                Method getter = findMethodInClassHierarchy(entity.getClass(), getterName);
                if (getter != null) {
                    getter.setAccessible(true);
                    Object value = getter.invoke(entity);
                    return value != null ? value.toString() : null;
                }

            } catch (Exception e) {
                log.warn("Failed to extract property '{}' from entity of type {}: {}",
                        propertyName, entity.getClass().getSimpleName(), e.getMessage());
            }
        }

        log.warn("Property '{}' not found in any of the provided entities", propertyName);
        return null;
    }

    private Field findFieldInClassHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    private Method findMethodInClassHierarchy(Class<?> clazz, String methodName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                return currentClass.getDeclaredMethod(methodName);
            } catch (NoSuchMethodException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    private String replaceTemplateVariables(String template, Map<String, String> variables) {
        if (template == null) return null;

        String result = template;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}"; // assuming template uses {{variableName}} format
            result = result.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
        }

        return result;
    }

    @Override
    public PageDto<com.hb.crm.core.beans.Notification.Notification> getAllNotificationsWithFilter(int page, int size, Boolean sent) {
        // Create sort order: unsent first, then by creation date descending
        Sort sort = Sort.by(
                Sort.Order.asc("sent"),  // false (unsent) comes before true (sent)
                Sort.Order.desc("lastTriedAt")
        );

        Pageable pageable = PageRequest.of(page, size, sort);
        Page<com.hb.crm.core.beans.Notification.Notification> notificationsPage;

        if (sent != null) {
            // Filter by sent status if provided
            notificationsPage = notificationRepository.findBySent(sent, pageable);
        } else {
            // Get all notifications if no filter provided
            notificationsPage = notificationRepository.findAll(pageable);
        }

        PageDto<com.hb.crm.core.beans.Notification.Notification> pageDto = new PageDto<>();
        pageDto.setItems(notificationsPage.getContent());
        pageDto.setTotalNoOfItems(notificationsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);

        return pageDto;
    }

    @Override
    public boolean retrySingleNotification(String notificationId) {
        Optional<com.hb.crm.core.beans.Notification.Notification> optionalNotification =
                notificationRepository.findById(notificationId);

        if (optionalNotification.isEmpty()) {
            log.error("Notification with ID {} not found", notificationId);
            return false;
        }

        com.hb.crm.core.beans.Notification.Notification notification = optionalNotification.get();

        try {
            String token = getUserFCMToken(notification.getUser());
            if (token == null) {
                log.error("No FCM token found for user ID: {}", notification.getUser().getId());
                return false;
            }

            boolean sent = sendNotification(token, notification.getSubject(), notification.getBody(), null);

            if (sent) {
                notification.setSent(true);
                notification.setLastTriedAt(LocalDateTime.now());
                notificationRepository.save(notification);
                log.info("Successfully resent notification ID: {}", notificationId);
                return true;
            } else {
                notification.setRetryCount(notification.getRetryCount() + 1);
                notification.setLastTriedAt(LocalDateTime.now());
                notificationRepository.save(notification);
                log.error("Failed to resend notification ID: {}", notificationId);
                return false;
            }

        } catch (Exception e) {
            notification.setRetryCount(notification.getRetryCount() + 1);
            notification.setLastTriedAt(LocalDateTime.now());
            notificationRepository.save(notification);
            log.error("Exception while resending notification ID: {}", notificationId, e);
            return false;
        }
    }

    @Override
    public GroupedNotificationsDto getGroupedNotifications(String userId, int page, int size) {
        // Get all notifications for the user (you might want to adjust this based on your existing logic)
        PageDto<NotificationDto> notifications = getNotifications(userId, page, size);

        return groupNotificationsByDate(notifications);
    }

    @Override
    public GroupedNotificationsDto getGroupedUnreadNotifications(String userId, int page, int size) {
        // Get unread notifications for the user
        PageDto<NotificationDto> notifications = getUnreadNotifications(userId, page, size);

        return groupNotificationsByDate(notifications);
    }

    private GroupedNotificationsDto groupNotificationsByDate(PageDto<NotificationDto> notifications) {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        List<NotificationDto> todayNotifications = new ArrayList<>();
        List<NotificationDto> yesterdayNotifications = new ArrayList<>();
        List<NotificationDto> olderNotifications = new ArrayList<>();

        for (NotificationDto notification : notifications.getItems()) {
            if (notification.getCreatedAt() != null) {
                LocalDate notificationDate = notification.getCreatedAt().toLocalDate();

                if (notificationDate.equals(today)) {
                    todayNotifications.add(notification);
                } else if (notificationDate.equals(yesterday)) {
                    yesterdayNotifications.add(notification);
                } else {
                    olderNotifications.add(notification);
                }
            } else {
                // Handle notifications without createdAt date - put them in older
                olderNotifications.add(notification);
            }
        }

        return new GroupedNotificationsDto(
                todayNotifications,
                yesterdayNotifications,
                olderNotifications,
                notifications.getTotalNoOfItems(),
                notifications.getPageNumber(),
                notifications.getItemsPerPage()
        );
    }

    @Override
    public void hideNotification(String notificationId, String userId) {
        // Find the notification and verify it belongs to the user
        Optional<com.hb.crm.core.beans.Notification.Notification> notificationOpt = notificationRepository.findById(notificationId);

        if (notificationOpt.isPresent()) {
            com.hb.crm.core.beans.Notification.Notification notification = notificationOpt.get();

            // Verify the notification belongs to the current user (security check)
            if (notification.getUser().getId().equals(userId)) {
                notification.setHidden(true);
                notificationRepository.save(notification);
            } else {
                throw new RuntimeException("Unauthorized: Cannot hide notification that doesn't belong to user");
            }
        } else {
            throw new RuntimeException("Notification not found with id: " + notificationId);
        }
    }

    @Override
    public void hideNotifications(List<String> notificationIds, String userId) {
        // Find all notifications by IDs
        List<com.hb.crm.core.beans.Notification.Notification> notifications = notificationRepository.findAllById(notificationIds);

        // Filter notifications that belong to the user and mark them as hidden
        List<com.hb.crm.core.beans.Notification.Notification> userNotifications = notifications.stream()
                .filter(notification -> notification.getUser().getId().equals(userId))
                .peek(notification -> notification.setHidden(true))
                .collect(Collectors.toList());

        // Save all updated notifications
        if (!userNotifications.isEmpty()) {
            notificationRepository.saveAll(userNotifications);
        }

        // Optional: Log if some notifications were skipped due to ownership
        if (userNotifications.size() != notificationIds.size()) {
            log.warn("Some notifications were skipped - user {} attempted to hide {} notifications but only {} belonged to them",
                    userId, notificationIds.size(), userNotifications.size());
        }
    }

    @Override
    public Long getUnreadNotificationsCount(String userId) {
        try {
            // Calculate date filter for last month (similar to your other notification methods)
            LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1);

            userRepository.findById(userId).orElseThrow(() -> new CustomException(404, "User not found"));

            // Use repository method to count unread notifications
            long count = notificationRepository.customCountMethod(
                    userId,
                    false,
                    false,
                    oneMonthAgo
            );

            log.debug("Found {} unread notifications for user: {}", count, userId);
            return count;

        } catch (Exception e) {
            log.error("Error counting unread notifications for user: {}", userId, e);
            throw new RuntimeException("Failed to count unread notifications", e);
        }
    }

    public String getUserFCMToken(User user) {
        String token = cashService.getUserFcmToken(user.getId());

        if (token == null)
            if (user.getFcmTokens() != null && !user.getFcmTokens().isEmpty())
                token = user.getFcmTokens().getFirst();

        return token;
    }

}