package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Employee;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;
import java.time.LocalDateTime;

public interface EmployeeRepository extends MongoRepository<Employee, String> {

    Employee findByUsername(String username);

    Optional<Employee> findByPassResetKey(String passkey);

    List<Employee> findByIsConnectedTrue();

    List<Employee> findByIsConnectedFalseAndLastConnectionDateBefore(LocalDateTime dateTime);
}
