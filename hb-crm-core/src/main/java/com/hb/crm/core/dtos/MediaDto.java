package com.hb.crm.core.dtos;

import com.hb.crm.core.Enums.ImageCategory;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.StoryOverlay;
import com.hb.crm.core.beans.Tag;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MediaDto {
    private String id;
    private MediaType type;
    private String caption;
    private String asset;
    private String url;
    private String videoUrl;
    private String source;
    private boolean mainImage;
    private List<Tag> tags;
    private ImageCategory imageCategory;
    private BigDecimal videoDuration;
    private BigDecimal videoDurationMS;
    private MediaType mediaType;
    private String clipStartTimecode;
    private String clipEndTimecode;
    private String thumbnailClipUrl;
    private String thumbnailCaptureUrl;
    private String description;
    private Double startTime;  // Add this
    private Double endTime;
    private SimpleUserinfoDto user;
    private float latitude;
    private float longtuid;
    private String audioUrl;
    private List<SimpleUserinfoDto> taggedUsers;
    private List<StoryOverlay> overlays;


}
