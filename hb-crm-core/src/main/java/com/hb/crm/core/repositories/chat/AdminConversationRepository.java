package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.beans.chat.OneChat.AdminConversation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdminConversationRepository extends MongoRepository<AdminConversation, String> {
    List<AdminConversation> findByConversationId(String conversationId);
}
