package com.hb.crm.client.dto;

import com.hb.crm.core.Enums.ImageCategory;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.StoryOverlay;
import com.hb.crm.core.beans.User;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.DBRef;


import java.math.BigDecimal;
import java.util.List;


@Data
public class CreateMediaDto {

    private String title;
    private String source;
    private String description;
    private String videoUrl;
    private ImageCategory imageCategory;
    private BigDecimal videoDuration;
    private BigDecimal videoDurationMS;
    private String thumbnailClipUrl;
    private String thumbnailCaptureUrl;
    private MediaType mediaType;
    private Double videoSize;
    private boolean mainImage=false;
    private float latitude;
    private float longtuid;
    private List<RefranceModelDto> taggedUsers;
    private List<StoryOverlay> overlays;
    private String clipStartTimecode;
    private String clipEndTimecode;
    private Double startTime;
    private Double endTime;

}
